﻿# GD_GameSystems_Notes.md - Sistemas (progresion, economia)

**Progresion**: Unlock lineal vs branching, power-ups vs habilidades
**Economia**: Input (collect), Output (spend), Balance (scarcity/abundance)
**Feedback loops**: Positive (snowball), Negative (catch-up), Neutral (steady)
**Emergencia**: Sistemas simples que crean comportamientos complejos
**Para prototipo**: 1 sistema core, otros en stub/mockup
