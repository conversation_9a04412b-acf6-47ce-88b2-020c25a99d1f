﻿# 00_Global_Instructions.md - Reglas comunes y variables

## Variables del proyecto
- **{GENRE}**: <PERSON><PERSON><PERSON> <PERSON> (platformer, puzzle, shooter, etc.)
- **{ENGINE}**: Motor de desarrollo (Unity, Godot, Unreal, Web, etc.)
- **{PLATFORM}**: Plataforma objetivo (Desktop, Mobile, Web)
- **{CAMERA}**: T<PERSON>o de camara (2D Side, 2D Top, 3D Third Person, etc.)
- **{INPUT}**: <PERSON><PERSON><PERSON> de entrada (Keyboard+Mouse, Touch, Gamepad)
- **{GOAL}**: Objetivo del prototipo (ej: "Validar core loop de salto")
- **{SCOPE_DAYS}**: Limite de tiempo del sprint (default: 7 dias)

## Reglas de trabajo
1. **UFM Mode**: Claro, conciso, 1 pregunta por turno maximo
2. **ShareLine obligatorio**: Cada respuesta termina con bloque ShareLine listo para copy-paste
3. **Accion siguiente**: Siempre especificar que debe hacer el siguiente rol
4. **Foco en M1**: Priorizar mecanica core funcional sobre features secundarias

## Formato ShareLine base
`
COPIA-> @destino: PlayForge Â· Fase: Prototipo v0 Â· Objetivo: {GOAL} Â· Decisiones: GENRE={...}, ENGINE={...}, PLATFORM={...}, CAMERA={...}, INPUT={...} Â· Estado: {...}% Â· Siguiente: {accion especifica}
`

## Flujo de trabajo
Designer (mini-GDD + M1/M2/M3) -> Artist (proxy pack) -> Dev (build jugable) -> Playtest -> Insights
