﻿# DEV_Scene_Architecture.md - <PERSON>, <PERSON><PERSON><PERSON><PERSON>er, Player

**Estructura base**: Main Scene -> GameController -> Player + UI + Environment
**GameController**: State machine (Menu/Playing/Paused/GameOver), score, timer
**Player**: Input handling, movement, collision, animation state
**Managers**: InputManager, AudioManager, UIManager (singletons simples)
**Scene flow**: MainMenu -> Game -> GameOver -> MainMenu (loop basico)
