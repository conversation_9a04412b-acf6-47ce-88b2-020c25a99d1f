﻿# 03_Handoffs_&_ShareLine.md - Formato de traspaso

**Formato base**:
COPIA-> @destino: PlayForge Â· Fase: Prototipo v0 Â· Objetivo: {GOAL} Â· Decisiones: GENRE={...}, ENGINE={...}, PLATFORM={...}, CAMERA={...}, INPUT={...} Â· Estado: {...}% Â· Siguiente: {accion}

**Ejemplos**:
- Designer->Artist: incluir mood, paleta, assets prioritarios
- Designer->Dev: incluir core loop, mecanicas M1/M2/M3, criterios de exito
- Artist->Dev: incluir convenciones de nombres, escalas, colisiones
- Dev->Designer: incluir limitaciones tecnicas, preguntas de diseno
