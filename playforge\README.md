﻿# PlayForge - Ecosistema de GPTs (estructura simplificada)

Prototipado rapido multi-motor con 3 GPTs especializados.

## Estructura
- **common/knowledge/**: Base de conocimiento compartida (7 archivos)
- **designer/knowledge/**: Conocimiento especifico de diseno (6 archivos)
- **artist/knowledge/**: Conocimiento especifico de arte (6 archivos)
- **dev/knowledge/**: Conocimiento especifico de programacion (6 archivos)
- **instructions/**: Prompts y operativa de cada GPT (4 archivos)

## Como usar
1. Copia el contenido de instructions/00_Global_Instructions.md + [Rol]_Instructions.md
2. Pega en el editor del GPT correspondiente
3. Adjunta todos los archivos de common/knowledge/ + [rol]/knowledge/
4. Usa el formato ShareLine para traspasos entre roles

Variables: {GENRE}{ENGINE}{PLATFORM}{CAMERA}{INPUT}{GOAL}{SCOPE_DAYS=7}
