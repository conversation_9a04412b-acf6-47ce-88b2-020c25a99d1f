﻿# ART_2D3D_Pipeline_Agnostic.md - Flujo de assets neutral

**2D**: PNG con transparencia, potencias de 2 (64x64, 128x128, 256x256)
**3D**: FBX/OBJ, triangulos < 1000 por objeto, texturas 512x512 max
**Sprites**: Atlas cuando sea posible, padding 2px, filtro Point para pixel art
**Modelos**: Aplicar transforms, centrar pivot, limpiar historia
**Export**: Mantener source files (.psd, .blend) separados de game assets
